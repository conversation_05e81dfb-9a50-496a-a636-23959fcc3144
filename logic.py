# logic.py

def generate_pseudocode(rule_data: dict) -> str:
    """
    根据从GUI收集的规则数据字典，生成标准化的中文伪代码。
    此函数不包含任何tkinter或sqlite3的依赖。

    Args:
        rule_data (dict): 包含所有GUI控件选择的字典。

    Returns:
        str: 格式化后的伪代码字符串。
    """
    
    # 伪代码中的字段名映射
    field_map = {
        "品牌/客户": "产品款式.品牌",
        "目标年龄段": "产品款式.目标年龄段",
        "物料化学分类": "物料化学分类",
        "物料使用位置": "物料使用位置",
        "物料颜色": "物料颜色",
        "供应商等级": "供应商等级",
    }
    
    # 1. 生成规则名称
    rule_name = rule_data.get('rule_name', "未命名规则")
    pseudocode = f'// 测试规则名称: \n  "{rule_name}"\n\n'

    # 2. 处理主体部分
    indent_level = 0
    main_if_needed = rule_data.get('requirement_source') == '客户要求' and rule_data.get('customer')

    # 主体开始
    pseudocode += "// 规则条件\n"

    if main_if_needed:
        customer = rule_data.get('customer')
        pseudocode += f"如果 (产品款式.品牌 == '{customer}')\n那么\n"
        indent_level += 1

    indent = "  " * indent_level

    # 3. 遍历条件块 (如果/否则如果)
    condition_blocks = rule_data.get('condition_blocks', [])
    for i, block in enumerate(condition_blocks):
        # 过滤掉 "不考虑此项" 的条件
        valid_conditions = [c for c in block['conditions'] if c['field'] != '-- 不考虑此项 --' and c['value']]
        
        # 如果没有有效条件，则跳过此块的IF/ELSEIF语句生成
        if not valid_conditions:
            continue

        # 构造条件语句 (A 并且 B 并且 C)
        condition_parts = []
        for cond in valid_conditions:
            field = field_map.get(cond['field'], cond['field'])
            op = cond['operator']
            val = cond['value']
            if op == '包含于':
                # 将逗号分隔的字符串转换为列表格式
                items = [f"'{item.strip()}'" for item in val.split(',')]
                formatted_val = f"[{', '.join(items)}]"
                condition_parts.append(f"{field} {op} {formatted_val}")
            else: # op == '=='
                condition_parts.append(f"{field} {op} '{val}'")
        
        condition_str = " 并且 ".join(condition_parts)

        # 添加 如果 / 否则如果
        if i == 0:
            pseudocode += f"{indent}如果 ({condition_str})\n"
        else:
            pseudocode += f"{indent}否则如果 ({condition_str})\n"
        
        pseudocode += f"{indent}那么\n"
        
        # 4. 构造行动逻辑 (创建测试要求)
        action_indent = "  " * (indent_level + 1)
        
        # 处理限值要求
        limit_value = block.get('limit', '').strip()
        if not limit_value or limit_value == rule_data.get('limit_placeholder'):
            limit_line = "依据限用物质表的对应要求"
        else:
            limit_line = f"'{limit_value}' // 用户输入"
            
        # 处理备注
        remarks = block.get('remarks', '').strip()
        remarks_line = ""
        if remarks:
            # 将多行备注合并为一行，并转义特殊字符
            formatted_remarks = ' '.join(remarks.splitlines()).replace("'", "\\'")
            remarks_line = f",\n{action_indent}  备注: '{formatted_remarks}'"

        pseudocode += f"{action_indent}创建测试要求 (\n"
        pseudocode += f"{action_indent}  限用物质: '{rule_data.get('substance', '')}',\n"
        pseudocode += f"{action_indent}  限值要求: {limit_line}{remarks_line}\n"
        pseudocode += f"{action_indent})\n"

    # 5. 闭合所有 '如果' 语句
    if any(c for b in condition_blocks for c in b['conditions'] if c['field'] != '-- 不考虑此项 --'):
         pseudocode += f"{indent}结束\n"

    if main_if_needed:
        pseudocode += "结束\n"

    return pseudocode.strip()