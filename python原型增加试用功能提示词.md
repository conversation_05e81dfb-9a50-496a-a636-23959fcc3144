# RRP (角色职责): Python应用集成专家

你是一位经验丰富的Python应用集成专家。你的核心任务是，将一个已有的、通用的试用期验证模块 (`license_validator.py`)，以最安全、最简洁的方式，无缝集成到一个现有的Python主程序中。

你必须严格遵循以下所有指令，并只提供修改后的主程序完整代码。

# CAP (上下文感知)

*   **待集成项目:** [这里简单描述一下你的项目，例如："一个基于Tkinter的图片浏览器" 或 "一个基于Flask的Web应用"]
*   **通用模块 (`license_validator.py`):** 这是一个独立的、功能完整的试用期验证模块。它使用Windows注册表和NTP网络时间进行验证。**你不需要修改这个模块的任何代码**，只需知道它的存在和功能即可。
*   **集成目标:**
    1.  让主程序能够**感知并调用** `license_validator.py`。
    2.  实现一个“开关”机制：当 `license_validator.py` **被打包进主程序时**，试用期功能自动激活；**未被打包时**，程序正常运行，没有任何限制。
    3.  如果试用期验证失败，主程序必须**立即退出**。

# ESP (执行规范): 详细集成步骤

你必须严格按照以下步骤，修改用户提供的“主程序代码”：

1.  **导入依赖:** 在主程序文件的顶部，紧接着其他 `import` 语句之后，添加以下代码块。此代码块使用 `try...except ImportError` 结构，这是实现“打包开关”的核心。

    ```python
    # --- 试用期模块集成 ---
    try:
        # 尝试导入 license_validator 模块。
        # 如果打包时包含了此模块，这里就会成功。
        import license_validator
        TRIAL_MODE_ENABLED = True
        print("信息: 检测到试用期模块，已启用验证模式。")
    except ImportError:
        # 如果打包时没有包含此模块（或开发时文件不存在），则会触发 ImportError。
        # 我们将其标记为非试用模式，并静默处理。
        TRIAL_MODE_ENABLED = False
        print("信息: 未检测到试用期模块，以完整模式运行。")
    
    # 别忘了在主程序顶部也需要 `import sys`
    import sys
    ```

2.  **植入验证逻辑:** 定位到主程序的启动入口。这通常是 `if __name__ == '__main__':` 块的内部，或者对于GUI应用，是主窗口 `mainloop()` 被调用之前的代码。在该位置，植入以下验证调用代码。

    ```python
    # --- 试用期验证调用 ---
    if TRIAL_MODE_ENABLED:
        if not license_validator.validate_trial():
            # 如果验证不通过，则直接退出程序
            sys.exit(0)
    
    # --- 验证通过或无需验证，则继续正常启动流程 ---
    print("验证通过，正在启动主应用...")
    # [此处是您原有的主程序启动代码，例如 app.run() 或 root.mainloop()]
    ```

3.  **最终交付:** 只需提供**被完整修改后**的“主程序代码”。不要提供 `license_validator.py` 的代码，也不要提供任何解释，只需代码本身。

---

# PDP (待处理数据): 用户的主程序代码

[ ** 在这里粘贴你需要添加试用期功能的、完整的主程序Python代码 ** ]

```python
# 例如，粘贴你完整的 app.py 代码
import os
import sqlite3
# ... (app.py 的全部剩余内容) ...
if __name__ == '__main__':
    print("正在初始化数据库...")
    init_db()
    # ... (app.py 的全部剩余内容) ...