# database.py

import sqlite3
import pandas as pd
import os


class Database:
    """
    负责所有与SQLite数据库的交互，包括表的创建、数据查询、
    以及通过Pandas进行的Excel导入/导出功能。
    """

    def __init__(self, db_name="rules_data.db"):
        """
        初始化数据库连接，并确保所有必要的表都已创建。
        使用相对路径确保可移植性。
        """
        # 获取脚本所在目录，确保db文件与脚本在同一位置
        base_dir = os.path.dirname(os.path.abspath(__file__))
        self.db_path = os.path.join(base_dir, db_name)
        self.conn = sqlite3.connect(self.db_path)
        self.cursor = self.conn.cursor()

        # 初始化配置表
        self._init_config_table()
        self.create_tables()

    def _init_config_table(self):
        """初始化配置表，存储用户自定义字段配置"""
        self.cursor.execute("""
            CREATE TABLE IF NOT EXISTS field_config (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                field_name TEXT NOT NULL UNIQUE,
                table_name TEXT NOT NULL,
                db_column TEXT NOT NULL,
                is_fixed INTEGER DEFAULT 0,
                display_order INTEGER DEFAULT 0
            )
        """)
        self.conn.commit()

    def _get_fixed_fields(self):
        """获取固定字段配置"""
        return {
            '品牌客户': {'table': 'Brands', 'column': 'BrandName'},
            '目标年龄段': {'table': 'AgeGroups', 'column': 'AgeGroupDescription'},
            '物料使用位置': {'table': 'ComponentLocations', 'column': 'LocationName'},
            '物料颜色': {'table': 'MaterialColors', 'column': 'ColorName'},
            '供应商等级': {'table': 'SupplierTiers', 'column': 'TierName'},
            '测试项目': {'table': 'TestItems', 'column': 'TestItemName'}
        }

    def _get_default_custom_fields(self):
        """获取默认自定义字段"""
        return ['物料化学分类', '限用物质']

    def _get_table_metadata(self):
        """动态获取表的元数据映射"""
        metadata = {}

        # 获取所有字段配置
        self.cursor.execute(
            "SELECT field_name, table_name, db_column FROM field_config ORDER BY display_order")
        fields = self.cursor.fetchall()

        for field_name, table_name, db_column in fields:
            metadata[table_name] = {
                'sheet': field_name,
                'db_col': db_column,
                'excel_col': field_name
            }

        return metadata

    def create_tables(self):
        """
        根据数据模型定义，创建所有需要的SQLite表。
        使用 'IF NOT EXISTS' 避免在表已存在时出错。
        """
        # 首先确保字段配置已初始化
        self._ensure_field_config_initialized()

        # 根据配置创建表
        metadata = self._get_table_metadata()
        for table, details in metadata.items():
            self.cursor.execute(f"""
                CREATE TABLE IF NOT EXISTS {table} (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    {details['db_col']} TEXT NOT NULL UNIQUE
                )
            """)
        self.conn.commit()
        print("数据库表检查/创建完成。")

    def _ensure_field_config_initialized(self):
        """确保字段配置已初始化"""
        # 检查是否已有配置
        self.cursor.execute("SELECT COUNT(*) FROM field_config")
        count = self.cursor.fetchone()[0]

        if count == 0:
            # 初始化固定字段
            fixed_fields = self._get_fixed_fields()
            order = 0
            for field_name, config in fixed_fields.items():
                self.cursor.execute("""
                    INSERT INTO field_config (field_name, table_name, db_column, is_fixed, display_order)
                    VALUES (?, ?, ?, 1, ?)
                """, (field_name, config['table'], config['column'], order))
                order += 1

            # 初始化默认自定义字段
            custom_fields = self._get_default_custom_fields()
            for field_name in custom_fields:
                table_name = self._generate_table_name(field_name)
                db_column = self._generate_column_name(field_name)
                self.cursor.execute("""
                    INSERT INTO field_config (field_name, table_name, db_column, is_fixed, display_order)
                    VALUES (?, ?, ?, 0, ?)
                """, (field_name, table_name, db_column, order))
                order += 1

            self.conn.commit()
        else:
            # 检查是否缺少新的固定字段（如测试项目）
            self._update_missing_fixed_fields()

    def _update_missing_fixed_fields(self):
        """更新缺少的固定字段"""
        fixed_fields = self._get_fixed_fields()

        for field_name, config in fixed_fields.items():
            # 检查字段是否已存在
            self.cursor.execute(
                "SELECT COUNT(*) FROM field_config WHERE field_name = ?", (field_name,))
            exists = self.cursor.fetchone()[0] > 0

            if not exists:
                # 获取当前最大的display_order
                self.cursor.execute(
                    "SELECT MAX(display_order) FROM field_config WHERE is_fixed = 1")
                max_order = self.cursor.fetchone()[0] or -1

                # 插入新的固定字段
                self.cursor.execute("""
                    INSERT INTO field_config (field_name, table_name, db_column, is_fixed, display_order)
                    VALUES (?, ?, ?, 1, ?)
                """, (field_name, config['table'], config['column'], max_order + 1))

                print(f"添加新的固定字段: {field_name}")

        self.conn.commit()

    def _generate_table_name(self, field_name):
        """根据字段名生成表名"""
        # 简单的映射规则
        name_map = {
            '物料化学分类': 'MaterialCategories',
            '限用物质': 'Substances'
        }
        return name_map.get(field_name, f"Custom_{field_name.replace(' ', '_')}")

    def _generate_column_name(self, field_name):
        """根据字段名生成列名"""
        name_map = {
            '物料化学分类': 'CategoryName',
            '限用物质': 'SubstanceName'
        }
        return name_map.get(field_name, f"{field_name.replace(' ', '_')}_Value")

    def get_current_fields(self):
        """获取当前所有字段配置"""
        self.cursor.execute("""
            SELECT field_name, is_fixed FROM field_config
            ORDER BY display_order
        """)
        return self.cursor.fetchall()

    def get_custom_fields(self):
        """获取当前自定义字段列表"""
        self.cursor.execute("""
            SELECT field_name FROM field_config
            WHERE is_fixed = 0
            ORDER BY display_order
        """)
        return [row[0] for row in self.cursor.fetchall()]

    def update_custom_fields(self, custom_fields):
        """更新自定义字段配置"""
        try:
            # 删除现有的自定义字段
            self.cursor.execute("DELETE FROM field_config WHERE is_fixed = 0")

            # 获取固定字段的最大order
            self.cursor.execute(
                "SELECT MAX(display_order) FROM field_config WHERE is_fixed = 1")
            max_order = self.cursor.fetchone()[0] or 0

            # 添加新的自定义字段
            order = max_order + 1
            for field_name in custom_fields:
                if field_name.strip():  # 忽略空字段
                    table_name = self._generate_table_name(field_name.strip())
                    db_column = self._generate_column_name(field_name.strip())
                    self.cursor.execute("""
                        INSERT INTO field_config (field_name, table_name, db_column, is_fixed, display_order)
                        VALUES (?, ?, ?, 0, ?)
                    """, (field_name.strip(), table_name, db_column, order))
                    order += 1

            self.conn.commit()

            # 重新创建表结构
            self._recreate_dynamic_tables()

            return True, "字段配置更新成功"
        except Exception as e:
            self.conn.rollback()
            return False, f"字段配置更新失败: {str(e)}"

    def _recreate_dynamic_tables(self):
        """重新创建动态表结构"""
        metadata = self._get_table_metadata()
        for table, details in metadata.items():
            # 检查表是否存在，如果不存在则创建
            self.cursor.execute(f"""
                CREATE TABLE IF NOT EXISTS {table} (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    {details['db_col']} TEXT NOT NULL UNIQUE
                )
            """)
        self.conn.commit()

    def get_all_data_for_table(self, table_name, column_name):
        """通用方法，从指定表的指定列获取所有数据。按插入顺序返回，保持与Excel中的顺序一致。"""
        self.cursor.execute(
            f"SELECT {column_name} FROM {table_name} ORDER BY id")
        return [row[0] for row in self.cursor.fetchall()]

    def get_data_for_field(self, field_name):
        """根据字段名获取数据"""
        self.cursor.execute("""
            SELECT table_name, db_column FROM field_config
            WHERE field_name = ?
        """, (field_name,))
        result = self.cursor.fetchone()

        if result:
            table_name, db_column = result
            return self.get_all_data_for_table(table_name, db_column)
        return []

    def get_all_field_names(self):
        """获取所有字段名称（按显示顺序）"""
        self.cursor.execute("""
            SELECT field_name FROM field_config
            ORDER BY display_order
        """)
        return [row[0] for row in self.cursor.fetchall()]

    # 为了向后兼容，保留一些常用的getter方法
    def get_all_brands(self):
        return self.get_data_for_field('品牌客户')

    def get_all_age_groups(self):
        return self.get_data_for_field('目标年龄段')

    def get_all_component_locations(self):
        return self.get_data_for_field('物料使用位置')

    def get_all_material_colors(self):
        return self.get_data_for_field('物料颜色')

    def get_all_supplier_tiers(self):
        return self.get_data_for_field('供应商等级')

    def get_all_test_items(self):
        return self.get_data_for_field('测试项目')

    def clear_and_insert_demo_data(self):
        """
        清空所有相关表，并插入预设的演示数据。
        同时重置字段配置为默认状态。
        """
        print("正在填充演示数据...")

        # 1. 重置字段配置为默认状态
        self.cursor.execute("DELETE FROM field_config")
        self._ensure_field_config_initialized()

        # 2. 重新创建表结构
        self._recreate_dynamic_tables()

        # 3. 填充演示数据
        demo_data = {
            '品牌客户': [('MG',), ('Zara',), ('OP',), ('Target',), ('WalMart',), ('所有客户',)],
            '目标年龄段': [('婴幼儿 (0-3岁)',), ('儿童 (3-14岁)',), ('成人 (14岁以上)',), ('所有年龄段',)],
            '物料使用位置': [('可直接接触皮肤的部件',), ('外壳可触及',), ('内里',), ('装饰性小部件',), ('所有位置',)],
            '物料颜色': [('黑色',), ('白色',), ('红色',), ('蓝色',), ('印花/多色',), ('所有颜色',)],
            '供应商等级': [('可靠供应商',), ('新供应商',), ('观察供应商',), ('所有供应商',)],
            '测试项目': [('总铅 (Total Lead)',), ('总镉 (Total Cadmium)',), ('偶氮染料 (Azo Dyes)',), ('邻苯二甲酸盐 (Phthalates)',), ('六价铬 (Chromium VI)',)],
            '物料化学分类': [('PVC/PU人造革',), ('金属配件',), ('染色纺织面料',), ('天然皮革',), ('泡棉/海绵',), ('塑料/橡胶组件',)],
            '限用物质': [('总铅 (Total Lead)',), ('总镉 (Total Cadmium)',), ('偶氮染料 (Azo Dyes)',), ('邻苯二甲酸盐 (Phthalates)',), ('六价铬 (Chromium VI)',)]
        }

        # 根据字段配置填充数据
        for field_name, data in demo_data.items():
            self.cursor.execute("""
                SELECT table_name, db_column FROM field_config
                WHERE field_name = ?
            """, (field_name,))
            result = self.cursor.fetchone()

            if result:
                table_name, db_column = result
                self.cursor.execute(f"DELETE FROM {table_name}")
                self.cursor.executemany(
                    f"INSERT INTO {table_name} ({db_column}) VALUES (?)", data)

        self.conn.commit()
        print("演示数据填充完成。")

    def export_to_excel(self, file_path="数据字典模板.xlsx"):
        """
        将所有数据库表导出到一个Excel文件中，每个表一个工作表。
        """
        metadata = self._get_table_metadata()
        try:
            with pd.ExcelWriter(file_path, engine='openpyxl') as writer:
                for table, details in metadata.items():
                    df = pd.read_sql_query(
                        f"SELECT {details['db_col']} FROM {table}", self.conn)
                    df.rename(
                        columns={details['db_col']: details['excel_col']}, inplace=True)
                    df.to_excel(
                        writer, sheet_name=details['sheet'], index=False)
            print(f"数据成功导出到 {file_path}")
            return True, f"成功导出到 {file_path}"
        except Exception as e:
            print(f"导出失败: {e}")
            return False, f"导出失败: {e}"

    def import_from_excel(self, file_path):
        """
        从指定的Excel文件导入数据，清空并替换数据库中的现有数据。
        """
        metadata = self._get_table_metadata()
        try:
            xls = pd.ExcelFile(file_path)
            for table, details in metadata.items():
                if details['sheet'] in xls.sheet_names:
                    df = pd.read_excel(xls, sheet_name=details['sheet'])
                    # 确保列名与预期匹配
                    if details['excel_col'] in df.columns:
                        # 清空旧数据
                        self.cursor.execute(f"DELETE FROM {table}")

                        # 准备要插入的数据
                        data_to_insert = df[[
                            details['excel_col']]].dropna().values.tolist()

                        # 插入新数据
                        self.cursor.executemany(
                            f"INSERT INTO {table} ({details['db_col']}) VALUES (?)", data_to_insert)
                    else:
                        raise ValueError(
                            f"工作表 '{details['sheet']}' 中缺少列 '{details['excel_col']}'")
            self.conn.commit()
            print(f"数据成功从 {file_path} 导入。")
            return True, f"成功从 {file_path} 导入"
        except Exception as e:
            self.conn.rollback()  # 如果出错则回滚
            print(f"导入失败: {e}")
            return False, f"导入失败: {e}"

    def __del__(self):
        """在对象销毁时关闭数据库连接。"""
        if self.conn:
            self.conn.close()
