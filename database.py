# database.py

import sqlite3
import pandas as pd
import os

class Database:
    """
    负责所有与SQLite数据库的交互，包括表的创建、数据查询、
    以及通过Pandas进行的Excel导入/导出功能。
    """
    def __init__(self, db_name="rules_data.db"):
        """
        初始化数据库连接，并确保所有必要的表都已创建。
        使用相对路径确保可移植性。
        """
        # 获取脚本所在目录，确保db文件与脚本在同一位置
        base_dir = os.path.dirname(os.path.abspath(__file__))
        self.db_path = os.path.join(base_dir, db_name)
        self.conn = sqlite3.connect(self.db_path)
        self.cursor = self.conn.cursor()
        self.create_tables()

    def _get_table_metadata(self):
        """内部辅助函数，返回所有表的元数据映射。"""
        return {
            'Substances': {'sheet': '限用物质', 'db_col': 'SubstanceName', 'excel_col': '物质名称'},
            'Brands': {'sheet': '品牌客户', 'db_col': 'BrandName', 'excel_col': '品牌名称'},
            'AgeGroups': {'sheet': '目标年龄段', 'db_col': 'AgeGroupDescription', 'excel_col': '年龄段描述'},
            'ComponentLocations': {'sheet': '物料使用位置', 'db_col': 'LocationName', 'excel_col': '位置名称'},
            'MaterialColors': {'sheet': '物料颜色', 'db_col': 'ColorName', 'excel_col': '颜色名称'},
            'SupplierTiers': {'sheet': '供应商等级', 'db_col': 'TierName', 'excel_col': '等级名称'},
            'MaterialCategories': {'sheet': '物料化学分类', 'db_col': 'CategoryName', 'excel_col': '分类名称'}
        }

    def create_tables(self):
        """
        根据数据模型定义，创建所有需要的SQLite表。
        使用 'IF NOT EXISTS' 避免在表已存在时出错。
        """
        metadata = self._get_table_metadata()
        for table, details in metadata.items():
            self.cursor.execute(f"""
                CREATE TABLE IF NOT EXISTS {table} (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    {details['db_col']} TEXT NOT NULL UNIQUE
                )
            """)
        self.conn.commit()
        print("数据库表检查/创建完成。")

    def get_all_data_for_table(self, table_name, column_name):
        """通用方法，从指定表的指定列获取所有数据。"""
        self.cursor.execute(f"SELECT {column_name} FROM {table_name} ORDER BY {column_name}")
        return [row[0] for row in self.cursor.fetchall()]

    # 为每个表提供便捷的 getter 方法
    def get_all_substances(self): return self.get_all_data_for_table('Substances', 'SubstanceName')
    def get_all_brands(self): return self.get_all_data_for_table('Brands', 'BrandName')
    def get_all_age_groups(self): return self.get_all_data_for_table('AgeGroups', 'AgeGroupDescription')
    def get_all_component_locations(self): return self.get_all_data_for_table('ComponentLocations', 'LocationName')
    def get_all_material_colors(self): return self.get_all_data_for_table('MaterialColors', 'ColorName')
    def get_all_supplier_tiers(self): return self.get_all_data_for_table('SupplierTiers', 'TierName')
    def get_all_material_categories(self): return self.get_all_data_for_table('MaterialCategories', 'CategoryName')
    
    def clear_and_insert_demo_data(self):
        """
        清空所有相关表，并插入预设的演示数据。
        """
        print("正在填充演示数据...")
        demo_data = {
            'Substances': [('总铅 (Total Lead)',), ('总镉 (Total Cadmium)',), ('偶氮染料 (Azo Dyes)',), ('邻苯二甲酸盐 (Phthalates)',), ('六价铬 (Chromium VI)',)],
            'Brands': [('LuxeBrand',), ('Zara',), ('ActiveSport',), ('KidsJoy',)],
            'AgeGroups': [('婴幼儿 (0-3岁)',), ('儿童 (3-14岁)',), ('成人 (14岁以上)',)],
            'ComponentLocations': [('可直接接触皮肤的部件',), ('外壳可触及',), ('内里',), ('装饰性小部件',)],
            'MaterialColors': [('黑色',), ('白色',), ('红色',), ('蓝色',), ('印花/多色',)],
            'SupplierTiers': [('可靠供应商',), ('新供应商',), ('观察供应商',)],
            'MaterialCategories': [('PVC/PU人造革',), ('金属配件',), ('染色纺织面料',), ('天然皮革',), ('泡棉/海绵',), ('塑料/橡胶组件',)]
        }
        
        metadata = self._get_table_metadata()
        for table, data in demo_data.items():
            db_col = metadata[table]['db_col']
            self.cursor.execute(f"DELETE FROM {table}")
            self.cursor.executemany(f"INSERT INTO {table} ({db_col}) VALUES (?)", data)
        
        self.conn.commit()
        print("演示数据填充完成。")

    def export_to_excel(self, file_path="数据字典模板.xlsx"):
        """
        将所有数据库表导出到一个Excel文件中，每个表一个工作表。
        """
        metadata = self._get_table_metadata()
        try:
            with pd.ExcelWriter(file_path, engine='openpyxl') as writer:
                for table, details in metadata.items():
                    df = pd.read_sql_query(f"SELECT {details['db_col']} FROM {table}", self.conn)
                    df.rename(columns={details['db_col']: details['excel_col']}, inplace=True)
                    df.to_excel(writer, sheet_name=details['sheet'], index=False)
            print(f"数据成功导出到 {file_path}")
            return True, f"成功导出到 {file_path}"
        except Exception as e:
            print(f"导出失败: {e}")
            return False, f"导出失败: {e}"

    def import_from_excel(self, file_path):
        """
        从指定的Excel文件导入数据，清空并替换数据库中的现有数据。
        """
        metadata = self._get_table_metadata()
        try:
            xls = pd.ExcelFile(file_path)
            for table, details in metadata.items():
                if details['sheet'] in xls.sheet_names:
                    df = pd.read_excel(xls, sheet_name=details['sheet'])
                    # 确保列名与预期匹配
                    if details['excel_col'] in df.columns:
                        # 清空旧数据
                        self.cursor.execute(f"DELETE FROM {table}")
                        
                        # 准备要插入的数据
                        data_to_insert = df[[details['excel_col']]].dropna().values.tolist()
                        
                        # 插入新数据
                        self.cursor.executemany(f"INSERT INTO {table} ({details['db_col']}) VALUES (?)", data_to_insert)
                    else:
                        raise ValueError(f"工作表 '{details['sheet']}' 中缺少列 '{details['excel_col']}'")
            self.conn.commit()
            print(f"数据成功从 {file_path} 导入。")
            return True, f"成功从 {file_path} 导入"
        except Exception as e:
            self.conn.rollback() # 如果出错则回滚
            print(f"导入失败: {e}")
            return False, f"导入失败: {e}"

    def __del__(self):
        """在对象销毁时关闭数据库连接。"""
        if self.conn:
            self.conn.close()