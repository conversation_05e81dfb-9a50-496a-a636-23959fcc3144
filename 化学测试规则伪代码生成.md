

# **角色 (Role): 资深Python应用开发者**

你是一名资深的Python开发者，尤其擅长使用Tkinter构建结构清晰、用户友好的桌面GUI应用程序，并精通使用SQLite和Pandas进行数据持久化与处理。你的代码风格严谨、注释清晰、追求高内聚、低耦合的软件架构。

---

### **[O] 核心目标 (Objective)**

你的任务是**完整地、精确地**实现一个独立的“产品化学测试规则伪代码生成器”桌面应用程序。此应用的核心功能是让合规专家通过GUI构建复杂的测试规则，并实时生成标准化的、人类可读的中文伪代码文本，用于知识库存档。

---

### **[E] 环境与约束 (Environment & Constraints)**

1.  **编程语言:** Python 3.x
2.  **GUI库:** **必须**使用Python标准库中的 `tkinter`。
3.  **数据处理:** **必须**使用 `pandas` 库处理Excel导入/导出。
4.  **数据库:** **必须**使用Python标准库中的 `sqlite3`。
5.  **路径:** 所有文件路径（尤其是数据库文件和Excel文件）**必须**使用相对路径，以确保打包成单文件可执行程序后能正常工作。数据库文件（例如 `rules_data.db`）应与最终的脚本或可执行文件在同一目录下。
6.  **代码结构:** **必须**遵循下面【实现蓝图】中定义的模块化文件结构。

---

### **[S] 成功标准 (Success Criteria)**

1.  **功能完整性:** 所有在【核心功能详述】中描述的功能点都已实现，无任何遗漏。
2.  **精确性:** 生成的伪代码格式与【中文伪代码规范】中提供的范例**完全一致**。
3.  **实时性:** GUI左侧“规则构造区”的任何操作，都能**立刻、自动地**在右侧“伪代码预览区”中反映出来。
4.  **数据管理:** “导出/导入数据字典”和“填充演示数据”功能必须能正常工作，并能正确读写`数据字典模板.xlsx`文件和更新SQLite数据库。
5.  **鲁棒性:** 程序运行稳定，对于“-- 不考虑此项 --”等边界条件能正确处理。
6.  **可交付性:** 最终交付的是一个组织良好、包含所有必要 `.py` 文件的项目，主入口为 `main.py`，可以直接运行。

---

### **核心功能详述 (Detailed Core Functions)**

你必须严格按照以下描述来实现所有功能。

#### **1. GUI界面设计**
*   双栏布局：左侧为“规则构造区”，右侧为“伪代码实时预览区”。

#### **2. 规则构造区 (左侧)**
*   **规则名称 (自动生成):**
    *   一个**只读**文本框，根据用户的选择**自动生成**，格式为：`[要求来源] - [目标年龄段] - [限用物质] - 管控规则`。
    *   **示例:** `"Zara - 儿童 (3-14岁) - 总铅 - 管控规则"`。
*   **规则元数据:**
    *   `限用物质`: **【必选】** 下拉菜单。
    *   `要求来源`: 单选按钮 (`内部要求` / `客户要求`)。选择`客户要求`时，动态显示`客户`下拉菜单。
*   **条件逻辑构造 (`如果 / 否则如果`):**
    *   提供“添加条件块”按钮，允许用户动态增加多个`如果` / `否则如果`块。
    *   每个块内可以动态添加多个`并且`关系的条件行。
    *   每个条件行包含 `判断字段`, `操作符 (== 或 包含于)`, `值` 三个部分。
    *   `判断字段`的下拉菜单中，**必须**包含一个 **`"-- 不考虑此项 --"`** 的选项。选中时，该条件行不出现在最终的伪代码中。
    *   **重要:** GUI中字段的排列顺序应遵循以下逻辑层次（从上到下）：
        1.  `品牌/客户`
        2.  `目标年龄段`
        3.  `物料化学分类`
        4.  `物料使用位置`
        5.  `物料颜色`
        6.  `供应商等级`
*   **行动逻辑 (`那么`):**
    *   **限值要求 (`Limit`):**
        *   **控件:** 文本输入框。
        *   **占位符 (Placeholder) 行为:**
            1.  初始为空时，显示灰色占位符文本：`“引用限用物质表的默认值”`。
            2.  用户输入内容后，占位符消失。
            3.  用户清空输入后，占位符恢复。
        *   **伪代码生成逻辑:**
            *   若为占位符状态，伪代码为 `限值要求: 依据限用物质表的对应要求`。
            *   若用户输入了值（如'30 ppm'），伪代码为 `限值要求: '30 ppm' // 用户自定义`。
    *   **备注:** 一个多行文本框，用于用户输入备注信息。

#### **3. 伪代码实时预览区 (右侧)**
*   一个**只读**的多行文本区，根据左侧操作**实时、自动**更新。
*   **伪代码范例 (你的目标输出格式):**
    ```
    // 自动生成的规则名称
    规则名称: "LuxeBrand - 全年龄段 - 镉(Cadmium) - 管控规则"
    
    // 规则主体
    如果 (产品款式.品牌 == 'LuxeBrand')
    那么
      // 规则一：针对儿童产品的高风险部件
      如果 (产品款式.目标年龄段 == '儿童 (3-14岁)' 并且 物料化学分类 包含于 ['PVC/PU人造革', '金属配件'] 并且 物料使用位置 == '可直接接触皮肤的部件')
      那么
        创建测试要求 (
          限用物质: '镉(Cadmium)',
          限值要求: '20 ppm', // 用户自定义
          备注: '儿童产品，高风险材质且直接接触皮肤，应用LuxeBrand儿童安全协议。'
        )
      // 规则二：其他情况
      否则如果 (产品款式.目标年龄段 == '成人 (14岁以上)')
      那么
        创建测试要求 (
          限用物质: '镉(Cadmium)',
          限值要求: 依据限用物质表的对应要求,
          备注: '成人产品，执行内部基础合规要求。'
        )
      结束如果
    结束如果
    ```

#### **4. 数据管理与初始化功能**
*   在GUI界面上提供三个按钮：“**导出数据字典 (Excel)**”, “**导入数据字典 (Excel)**”, “**填充演示数据**”。
*   **导出:** 读取所有数据库表，按【数据映射表】规范生成一个名为 `数据字典模板.xlsx` 的Excel文件。
*   **导入:** 允许用户选择一个Excel文件，**清空并替换**数据库中对应表的数据。
*   **填充演示数据:** 弹窗警告确认后，清空所有数据表，并写入下表中的【预设演示数据】。

#### **5. 数据模型 (数据库与Excel映射)**
你需要在程序首次运行时，检查并创建以下SQLite表。

| 数据库表名 (`TableName`) | Excel工作表中文别名 | 数据库列名 (`ColumnName`) | Excel表头中文列名 | **预设演示数据 (Demo Data)**                                 |
| :----------------------- | :------------------ | :------------------------ | :---------------- | :----------------------------------------------------------- |
| `Substances`             | 限用物质            | `SubstanceName`           | 物质名称          | `总铅 (Total Lead)`, `总镉 (Total Cadmium)`, `偶氮染料 (Azo Dyes)`, `邻苯二甲酸盐 (Phthalates)`, `六价铬 (Chromium VI)` |
| `Brands`                 | 品牌客户            | `BrandName`               | 品牌名称          | `LuxeBrand`, `Zara`, `ActiveSport`, `KidsJoy`                |
| `AgeGroups`              | 目标年龄段          | `AgeGroupDescription`     | 年龄段描述        | `婴幼儿 (0-3岁)`, `儿童 (3-14岁)`, `成人 (14岁以上)`         |
| `ComponentLocations`     | 物料使用位置        | `LocationName`            | 位置名称          | `可直接接触皮肤的部件`, `外壳可触及`, `内里`, `装饰性小部件` |
| `MaterialColors`         | 物料颜色            | `ColorName`               | 颜色名称          | `黑色`, `白色`, `红色`, `蓝色`, `印花/多色`                  |
| `SupplierTiers`          | 供应商等级          | `TierName`                | 等级名称          | `可靠供应商`, `新供应商`, `观察供应商`                       |
| `MaterialCategories`     | 物料化学分类        | `CategoryName`            | 分类名称          | `PVC/PU人造革`, `金属配件`, `染色纺织面料`, `天然皮革`, `泡棉/海绵`, `塑料/橡胶组件` |

---

### **实现蓝图 (Implementation Blueprint)**

请严格按照以下模块化结构组织你的代码，创建对应的 `.py` 文件。

#### **`database.py`**
*   **职责:** 负责所有与SQLite数据库的交互。
*   **实现:**
    *   定义一个 `Database` 类。
    *   `__init__`: 连接到 `rules_data.db` 文件，如果表不存在则调用方法创建它们。
    *   `create_tables()`: 根据【数据模型】创建所有表。
    *   为每个表创建 `get_all_...()` 方法（例如 `get_all_brands()`），用于填充GUI下拉菜单。
    *   `clear_and_insert_demo_data()`: 实现“填充演示数据”功能，使用【数据模型】中定义的演示数据。
    *   `export_to_excel()`: 实现“导出数据字典”功能。使用 `pandas` 创建Excel文件。
    *   `import_from_excel()`: 实现“导入数据字典”功能。使用 `pandas` 读取Excel，并执行“清空并替换”逻辑。

#### **`logic.py`**
*   **职责:** 包含所有核心业务逻辑，尤其是伪代码的生成。
*   **实现:**
    *   创建一个函数 `generate_pseudocode(rule_data)`。
    *   `rule_data` 是一个字典或数据类，包含了从GUI收集的所有当前用户的选择。
    *   此函数**不应**有任何 `tkinter` 或 `sqlite3` 的代码。它只负责数据到文本的转换。
    *   根据输入的 `rule_data`，精确地构建出符合【中文伪代码规范】的字符串，并返回。
    *   处理所有逻辑，如忽略 `"-- 不考虑此项 --"` 的条件、处理占位符限值等。

#### **`gui.py`**
*   **职责:** 负责构建和管理所有Tkinter界面元素和事件处理。
*   **实现:**
    *   定义一个 `Application` 类，继承自 `tk.Frame`。
    *   `__init__`: 初始化数据库连接 (`Database`类的实例)，并调用 `create_widgets()`。
    *   `create_widgets()`: 创建所有的GUI组件（标签、按钮、下拉菜单、文本框等）。
    *   `_load_initial_data()`: 从数据库加载数据并填充所有下拉菜单。
    *   **核心:** 实现一个 `_update_pseudocode_preview(*args)` 方法。这个方法将是所有GUI控件变化的事件回调函数。
    *   `_update_pseudocode_preview` 的逻辑：
        1.  从所有GUI控件收集当前用户的选择，整合成一个 `rule_data` 字典。
        2.  调用 `logic.generate_pseudocode(rule_data)` 获取最新的伪代码字符串。
        3.  更新右侧“伪代码实时预览区”的文本。
    *   为所有需要触发更新的控件（下拉菜单、单选按钮、文本框输入等）绑定 `_update_pseudocode_preview` 事件。
    *   实现数据管理按钮（导出、导入、填充演示数据）的回调函数，这些函数将调用 `database.py` 中对应的方法，并在操作后刷新界面数据。

#### **`main.py`**
*   **职责:** 应用程序的主入口。
*   **实现:**
    *   导入 `tkinter` 和 `gui.py` 中的 `Application` 类。
    *   创建主窗口 `root = tk.Tk()`。
    *   设置窗口标题和初始大小。
    *   创建 `Application` 类的实例。
    *   启动 `root.mainloop()`。

请开始编写代码。首先从 `database.py` 开始，然后是 `logic.py`，接着是 `gui.py`，最后是 `main.py`。确保每个文件都包含清晰的注释。