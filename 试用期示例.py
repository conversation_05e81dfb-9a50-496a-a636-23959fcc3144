import os
import sqlite3
import json
from datetime import datetime, timedelta, date
import calendar
import io
import webbrowser
from flask import Flask, render_template, request, jsonify, send_file
import openpyxl
from openpyxl.utils import get_column_letter
from openpyxl.styles import Font, Border, Side, Alignment, PatternFill
import sys

try:
    import license_validator
    TRIAL_MODE_ENABLED = True
except ImportError:
    # 如果打包时没有包含此模块（或开发时文件不存在），则会触发 ImportError。
    TRIAL_MODE_ENABLED = False

# --- 兼容开发环境和打包（frozen）环境的路径设置 ---
if getattr(sys, 'frozen', False):
    application_path = os.path.dirname(sys.executable)
else:
    application_path = os.path.dirname(os.path.abspath(__file__))

def resource_path(relative_path):
    try:
        base_path = sys._MEIPASS
    except Exception:
        base_path = os.path.abspath(".")
    return os.path.join(base_path, relative_path)

app = Flask(
    __name__,
    template_folder=resource_path('templates'),
    static_folder=resource_path('static')
)
PORT = 6478
DATABASE_PATH = os.path.join(application_path, 'database.db')

# --- 数据库初始化与辅助函数 ---
def get_db_connection():
    conn = sqlite3.connect(DATABASE_PATH)
    conn.row_factory = sqlite3.Row
    return conn

def init_db():
    with get_db_connection() as conn:
        cursor = conn.cursor()
        cursor.execute('CREATE TABLE IF NOT EXISTS holidays (holiday_date TEXT PRIMARY KEY)')
        cursor.execute('CREATE TABLE IF NOT EXISTS project_parameters (param_name TEXT PRIMARY KEY, param_value TEXT)')
        cursor.execute('CREATE TABLE IF NOT EXISTS daily_work_hours (record_date TEXT PRIMARY KEY, planned_hours REAL, is_modified INTEGER DEFAULT 0)')
        cursor.execute('CREATE TABLE IF NOT EXISTS monthly_summary (month_key TEXT PRIMARY KEY, normal_hours REAL, normal_overtime REAL, special_overtime REAL)')
        default_params = {'default_hour_weekday': '10', 'default_hour_saturday': '8', 'default_hour_special': '0'}
        for name, value in default_params.items():
            cursor.execute("INSERT OR IGNORE INTO project_parameters (param_name, param_value) VALUES (?, ?)", (name, value))
        conn.commit()

def get_parameter(param_name, default_value=None):
    with get_db_connection() as conn:
        param = conn.execute('SELECT param_value FROM project_parameters WHERE param_name = ?', (param_name,)).fetchone()
        return param['param_value'] if param else default_value

def set_parameter(param_name, param_value):
    with get_db_connection() as conn:
        conn.execute('REPLACE INTO project_parameters (param_name, param_value) VALUES (?, ?)', (param_name, param_value))
        conn.commit()

def calculate_single_day_hours(record_date_str, planned_hours, holidays_set):
    dt = datetime.strptime(record_date_str, '%Y-%m-%d').date()
    day_of_week = dt.weekday()
    normal_hours, normal_overtime, special_overtime = 0, 0, 0
    is_holiday = record_date_str in holidays_set
    if is_holiday or day_of_week == 6:
        special_overtime = planned_hours
    elif day_of_week == 5:
        normal_overtime = planned_hours
    else:
        if planned_hours > 8:
            normal_hours = 8
            normal_overtime = planned_hours - 8
        else:
            normal_hours = planned_hours
    return {"normal_hours": normal_hours, "normal_overtime": normal_overtime, "special_overtime": special_overtime}

def calculate_monthly_hours(daily_hours_data, holidays_set):
    monthly_totals = {}
    for record_date_str, planned_hours in daily_hours_data.items():
        month_key = record_date_str[:7]
        if month_key not in monthly_totals:
            monthly_totals[month_key] = {'normal_hours': 0, 'normal_overtime': 0, 'special_overtime': 0}
        day_breakdown = calculate_single_day_hours(record_date_str, planned_hours, holidays_set)
        monthly_totals[month_key]['normal_hours'] += day_breakdown['normal_hours']
        monthly_totals[month_key]['normal_overtime'] += day_breakdown['normal_overtime']
        monthly_totals[month_key]['special_overtime'] += day_breakdown['special_overtime']
    return monthly_totals

@app.route('/')
def index():
    return render_template('index.html')

@app.route('/api/get-initial-data', methods=['GET'])
def get_initial_data():
    with get_db_connection() as conn:
        params = {p['param_name']: p['param_value'] for p in conn.execute('SELECT param_name, param_value FROM project_parameters').fetchall()}
        holidays = [h['holiday_date'] for h in conn.execute('SELECT holiday_date FROM holidays').fetchall()]
        daily_hours = {d['record_date']: {'planned_hours': d['planned_hours'], 'is_modified': d['is_modified']} for d in conn.execute('SELECT record_date, planned_hours, is_modified FROM daily_work_hours').fetchall()}
        summary = {s['month_key']: dict(s) for s in conn.execute('SELECT * FROM monthly_summary').fetchall()}
    return jsonify({'parameters': params, 'holidays': holidays, 'daily_work_hours': daily_hours, 'monthly_summary': summary})

@app.route('/api/save-settings', methods=['POST'])
def save_settings():
    data = request.json
    try:
        set_parameter('default_hour_weekday', str(float(data.get('weekday'))))
        set_parameter('default_hour_saturday', str(float(data.get('saturday'))))
        set_parameter('default_hour_special', str(float(data.get('special'))))
        return jsonify({'status': 'success', 'message': '设置已成功保存！'})
    except (ValueError, TypeError):
        return jsonify({'status': 'error', 'message': '输入值无效，请输入数字。'}), 400

@app.route('/api/upload-holidays', methods=['POST'])
def upload_holidays():
    if 'holiday_file' not in request.files: return jsonify({'status': 'error', 'message': '未找到文件'}), 400
    file = request.files['holiday_file']
    if file.filename == '': return jsonify({'status': 'error', 'message': '未选择文件'}), 400
    try:
        workbook = openpyxl.load_workbook(file)
        sheet = workbook.active
        new_holidays = []
        for row in sheet.iter_rows(min_row=2, values_only=True):
            if row[0]:
                if isinstance(row[0], datetime): holiday_date_str = row[0].strftime('%Y-%m-%d')
                else:
                    datetime.strptime(str(row[0]), '%Y-%m-%d')
                    holiday_date_str = str(row[0])
                new_holidays.append((holiday_date_str,))
    except Exception as e: return jsonify({'status': 'error', 'message': f'文件处理失败：请确保第一列为 YYYY-MM-DD 格式的日期。错误详情: {e}'}), 400
    if not new_holidays: return jsonify({'status': 'error', 'message': '文件中未找到有效的日期数据。'}), 400
    with get_db_connection() as conn:
        cursor = conn.cursor()
        cursor.execute('DELETE FROM holidays')
        cursor.executemany('INSERT INTO holidays (holiday_date) VALUES (?)', new_holidays)
        conn.commit()
    return jsonify({'status': 'success', 'message': f'成功导入 {len(new_holidays)} 个节假日。','holidays': [h[0] for h in new_holidays]})

@app.route('/api/calculate', methods=['POST'])
def perform_calculation():
    data = request.json
    start_date_str, end_date_str, daily_hours = data.get('start_date'), data.get('end_date'), data.get('daily_hours', {})
    if not all([start_date_str, end_date_str, daily_hours]): return jsonify({'status': 'error', 'message': '请求数据不完整。'}), 400
    with get_db_connection() as conn:
        cursor = conn.cursor()
        set_parameter('start_date', start_date_str)
        set_parameter('end_date', end_date_str)
        cursor.execute('DELETE FROM daily_work_hours')
        daily_hours_to_db = [(day_str, float(day_data['planned_hours']), int(day_data['is_modified'])) for day_str, day_data in daily_hours.items()]
        cursor.executemany('INSERT INTO daily_work_hours (record_date, planned_hours, is_modified) VALUES (?, ?, ?)', daily_hours_to_db)
        holidays_set = {h['holiday_date'] for h in cursor.execute('SELECT holiday_date FROM holidays').fetchall()}
        planned_hours_for_calc = {item[0]: item[1] for item in daily_hours_to_db}
        monthly_summary_data = calculate_monthly_hours(planned_hours_for_calc, holidays_set)
        cursor.execute('DELETE FROM monthly_summary')
        summary_to_db = [(k, v['normal_hours'], v['normal_overtime'], v['special_overtime']) for k, v in monthly_summary_data.items()]
        cursor.executemany('INSERT INTO monthly_summary (month_key, normal_hours, normal_overtime, special_overtime) VALUES (?, ?, ?, ?)', summary_to_db)
        conn.commit()
    return jsonify({'status': 'success', 'summary': monthly_summary_data})

@app.route('/api/export-excel', methods=['GET'])
def export_excel():
    header_font = Font(bold=True, name='等线', size=12)
    center_alignment = Alignment(horizontal='center', vertical='center')
    thin_border_side = Side(style='thin')
    thin_border = Border(left=thin_border_side, right=thin_border_side, top=thin_border_side, bottom=thin_border_side)
    header_fill = PatternFill(start_color="8db4e2", end_color="8db4e2", fill_type="solid")

    def apply_styles_to_sheet(ws):
        for cell in ws[1]:
            cell.font = header_font
            cell.alignment = center_alignment
            cell.border = thin_border
            cell.fill = header_fill
        for row in ws.iter_rows(min_row=2):
            for cell in row:
                cell.alignment = center_alignment
                cell.border = thin_border
        for col in ws.columns:
            max_length = 0
            column = get_column_letter(col[0].column)
            for cell in col:
                try:
                    cell_len = 0
                    if cell.value:
                        for char in str(cell.value):
                             cell_len += 2 if '\u4e00' <= char <= '\u9fff' else 1
                    if cell_len > max_length: max_length = cell_len
                except: pass
            adjusted_width = (max_length + 2)
            ws.column_dimensions[column].width = adjusted_width

    wb = openpyxl.Workbook()
    with get_db_connection() as conn:
        ws1 = wb.active
        ws1.title = "月度汇总"
        ws1.append(["月份", "正常工作小时", "正常加班小时", "周日和公共假期加班小时"])
        summary_data = conn.execute('SELECT * FROM monthly_summary ORDER BY month_key').fetchall()
        for row in summary_data: ws1.append([row['month_key'], row['normal_hours'], row['normal_overtime'], row['special_overtime']])
        
        ws2 = wb.create_sheet(title="排班明细")
        ws2.append(["日期", "星期", "是否节假日", "计划工时", "正常上班小时", "正常加班小时", "周日和公共假期加班小时"])
        weekdays_cn = ["一", "二", "三", "四", "五", "六", "日"]
        holidays_set = {h['holiday_date'] for h in conn.execute('SELECT holiday_date FROM holidays').fetchall()}
        daily_data = conn.execute('SELECT record_date, planned_hours FROM daily_work_hours ORDER BY record_date').fetchall()
        for row in daily_data:
            record_date_str, planned_hours = row['record_date'], row['planned_hours']
            dt = datetime.strptime(record_date_str, '%Y-%m-%d').date()
            weekday_str, is_holiday_str = f"周{weekdays_cn[dt.weekday()]}", "是" if record_date_str in holidays_set else "否"
            breakdown = calculate_single_day_hours(record_date_str, planned_hours, holidays_set)
            ws2.append([record_date_str, weekday_str, is_holiday_str, planned_hours, breakdown['normal_hours'], breakdown['normal_overtime'], breakdown['special_overtime']])
        
        apply_styles_to_sheet(ws1)
        apply_styles_to_sheet(ws2)

    file_stream = io.BytesIO()
    wb.save(file_stream)
    file_stream.seek(0)
    return send_file( file_stream, as_attachment=True, download_name=f'工时预算_{datetime.now().strftime("%Y%m%d%H%M")}.xlsx', mimetype='application/vnd.openxmlformats-officedocument.spreadsheetml.sheet')

# --- 应用启动  ---
if __name__ == '__main__':
    if TRIAL_MODE_ENABLED:
        if not license_validator.validate_trial():
            sys.exit(0)
    # --- 验证通过或无需验证，则继续正常启动流程 ---
    print("正在初始化数据库...")
    init_db()
    print("数据库准备就绪。")
    url = f"http://127.0.0.1:{PORT}"
    webbrowser.open_new(url)
    print(f"启动Flask应用，请在浏览器中访问 {url}")
    app.run(port=PORT, debug=True, use_reloader=False)
