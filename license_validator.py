import winreg
import ntplib
import socket
from datetime import datetime
import tkinter as tk
from tkinter import messagebox
import threading # [FIX] 引入 threading 模块

# --- 常量定义 ---
TRIAL_DAYS = 15
REG_PATH = r"Software\JiaNingApp"
REG_KEY_NAME = "InstallDate"
NTP_SERVERS = [
    "0.cn.pool.ntp.org", "1.cn.pool.ntp.org", "2.cn.pool.ntp.org", 
    "3.cn.pool.ntp.org", "ntp.ntsc.ac.cn", "time.windows.com"
]

def _show_message_box(title, message):
    """在一个独立的线程中显示消息框，防止主GUI卡顿"""
    root = tk.Tk()
    root.withdraw()
    messagebox.showinfo(title, message)
    root.destroy()

# [FIX] 使用并发模型重构 _get_network_time 函数
def _get_network_time():
    """
    并发地从NTP服务器列表获取当前日期。
    只要有一个服务器成功返回，就立即返回结果。
    """
    result_list = []
    result_found_event = threading.Event()
    lock = threading.Lock()

    def _ntp_worker(server):
        """每个线程执行的工作函数"""
        # 如果已有其他线程成功，则提前退出
        if result_found_event.is_set():
            return

        try:
            client = ntplib.NTPClient()
            response = client.request(server, version=3, timeout=2)
            network_date = datetime.fromtimestamp(response.tx_time).date()
            
            # 使用锁来确保线程安全地写入结果
            with lock:
                # 再次检查，防止在等待锁的过程中其他线程已成功
                if not result_found_event.is_set():
                    result_list.append(network_date)
                    result_found_event.set() # 发送信，通知主线程和其他线程已成功
        except (ntplib.NTPException, socket.gaierror, socket.timeout):
            # 静默处理失败
            pass

    # 为每个服务器创建一个线程
    threads = [threading.Thread(target=_ntp_worker, args=(server,)) for server in NTP_SERVERS]
    
    # 启动所有线程
    for t in threads:
        t.start()
        
    # 等待事件被设置，或者等待一个总的超时时间（例如4秒）
    # event.wait() 会在事件被任一线程set()后立即返回True
    success = result_found_event.wait(timeout=4.0)

    if success and result_list:
        print(f"成功从NTP服务器获取时间: {result_list[0]}")
        return result_list[0]
    else:
        print("警告: 在规定时间内无法从任何NTP服务器获取时间。")
        return None


def _get_first_run_date():
    """从注册表读取首次运行日期，不存在则返回None"""
    try:
        key = winreg.OpenKey(winreg.HKEY_CURRENT_USER, REG_PATH, 0, winreg.KEY_READ)
        value, _ = winreg.QueryValueEx(key, REG_KEY_NAME)
        winreg.CloseKey(key)
        return datetime.strptime(value, "%Y-%m-%d").date()
    except FileNotFoundError:
        return None
    except Exception as e:
        print(f"错误: 读取注册表失败 - {e}")
        return None

def _set_first_run_date(install_date):
    """将首次运行日期写入注册表"""
    try:
        key = winreg.CreateKey(winreg.HKEY_CURRENT_USER, REG_PATH)
        winreg.SetValueEx(key, REG_KEY_NAME, 0, winreg.REG_SZ, install_date.strftime("%Y-%m-%d"))
        winreg.CloseKey(key)
        return True
    except Exception as e:
        print(f"错误: 写入注册表失败 - {e}")
        return False

def validate_trial():
    """
    执行试用期验证的核心函数。
    返回 True 表示验证通过，可以继续运行。
    返回 False 表示验证失败，主程序应退出。
    """
    print("...启动试用期验证流程...")
    first_run_date = _get_first_run_date()
    
    if first_run_date is None:
        print("检测到首次运行。正在从网络获取时间并注册...")
        current_date = _get_network_time()
        
        if current_date is None:
            _show_message_box("验证错误", "无法连接到时间服务器，请检查您的网络连接后重试。")
            return False
            
        if _set_first_run_date(current_date):
            message = f"欢迎使用！\n\n本软件为试用版，有效期为 {TRIAL_DAYS} 天。"
            _show_message_box("首次运行提示", message)
            return True
        else:
            _show_message_box("注册失败", "无法写入注册表信息，请检查权限。")
            return False

    else:
        print(f"已注册，首次运行日期: {first_run_date}")
        current_date = _get_network_time()

        if current_date is None:
            _show_message_box("验证错误", "无法连接到时间服务器，请检查您的网络连接后重试。")
            return False

        days_elapsed = (current_date - first_run_date).days
        days_left = TRIAL_DAYS - days_elapsed

        if days_left < 0:
            message = f"您的 {TRIAL_DAYS} 天试用期已结束，感谢您的使用！"
            _show_message_box("试用已到期", message)
            return False
        else:
            message = f"软件试用期还剩余 {days_left} 天。"
            _show_message_box("试用提醒", message)
            return True