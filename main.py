# main.py

import tkinter as tk
from gui import Application

# --- 试用期模块集成 ---
try:
    # 尝试导入 license_validator 模块。
    # 如果打包时包含了此模块，这里就会成功。
    import license_validator
    TRIAL_MODE_ENABLED = True
    print("信息: 检测到试用期模块，已启用验证模式。")
except ImportError:
    # 如果打包时没有包含此模块（或开发时文件不存在），则会触发 ImportError。
    # 我们将其标记为非试用模式，并静默处理。
    TRIAL_MODE_ENABLED = False
    print("信息: 未检测到试用期模块，以完整模式运行。")

import sys


def main():
    """
    应用程序的主入口函数。
    """
    root = tk.Tk()
    root.title("产品测试规则生成器")

    # 设置一个合理的初始窗口大小
    root.geometry("1400x850")

    # 设置窗口最小尺寸，防止界面元素被过度挤压
    root.minsize(1000, 600)

    app = Application(master=root)

    # 启动Tkinter事件循环
    root.mainloop()


if __name__ == "__main__":
    # --- 试用期验证调用 ---
    if TRIAL_MODE_ENABLED:
        if not license_validator.validate_trial():
            # 如果验证不通过，则直接退出程序
            sys.exit(0)

    # --- 验证通过或无需验证，则继续正常启动流程 ---
    print("验证通过，正在启动主应用...")
    main()
