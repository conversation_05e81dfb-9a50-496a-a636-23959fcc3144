# gui.py

import tkinter as tk
from tkinter import ttk, messagebox, filedialog
from database import Database
from logic import generate_pseudocode


class Application(ttk.Frame):
    """
    主应用程序GUI类，负责构建和管理所有Tkinter界面元素和事件处理。
    """
    NO_SELECTION = "-- 不考虑此项 --"
    LIMIT_PLACEHOLDER = "引用限用物质表的默认值"

    def __init__(self, master=None):
        super().__init__(master)
        self.master = master
        self.pack(fill="both", expand=True)

        self.db = Database()

        # 用于存储动态创建的控件引用
        self.condition_block_widgets = []

        # GUI状态变量
        self.source_var = tk.StringVar(value="内部要求")
        self.substance_var = tk.StringVar()
        self.customer_var = tk.StringVar()
        self.rule_name_var = tk.StringVar()
        self.main_age_group_var = tk.StringVar()  # 用于规则名称生成

        self._create_widgets()
        self._load_initial_data()
        self._add_condition_block()  # 默认创建一个条件块
        self._update_pseudocode_preview()

    def _create_widgets(self):
        # 使用PanedWindow创建可调整的双栏布局
        main_pane = ttk.PanedWindow(self, orient=tk.HORIZONTAL)
        main_pane.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)

        # 左侧：规则构造区
        constructor_frame = self._create_constructor_panel(main_pane)
        main_pane.add(constructor_frame, weight=1)

        # 右侧：伪代码预览区
        preview_frame = self._create_preview_panel(main_pane)
        main_pane.add(preview_frame, weight=1)

    def _create_constructor_panel(self, parent):
        frame = ttk.Frame(parent, padding="10")

        # 1. 数据管理区
        data_mgmt_frame = ttk.LabelFrame(frame, text="数据管理", padding="10")
        data_mgmt_frame.pack(fill=tk.X, expand=True, pady=(0, 10))
        ttk.Button(data_mgmt_frame, text="导出数据字典 (Excel)",
                   command=self._export_data).pack(side=tk.LEFT, padx=5)
        ttk.Button(data_mgmt_frame, text="导入数据字典 (Excel)",
                   command=self._import_data).pack(side=tk.LEFT, padx=5)
        ttk.Button(data_mgmt_frame, text="填充演示数据",
                   command=self._populate_demo_data).pack(side=tk.LEFT, padx=5)

        # 2. 规则元数据区
        metadata_frame = ttk.LabelFrame(frame, text="规则元数据", padding="10")
        metadata_frame.pack(fill=tk.X, expand=True, pady=(0, 10))

        ttk.Label(metadata_frame, text="规则名称:").grid(
            row=0, column=0, sticky=tk.W, padx=5, pady=2)
        ttk.Entry(metadata_frame, textvariable=self.rule_name_var, state='readonly').grid(
            row=0, column=1, columnspan=3, sticky='ew', padx=5, pady=2)

        ttk.Label(metadata_frame, text="限用物质:").grid(
            row=1, column=0, sticky=tk.W, padx=5, pady=2)
        self.substance_combo = ttk.Combobox(
            metadata_frame, textvariable=self.substance_var, state='readonly')
        self.substance_combo.grid(
            row=1, column=1, columnspan=3, sticky='ew', padx=5, pady=2)

        ttk.Label(metadata_frame, text="要求来源:").grid(
            row=2, column=0, sticky=tk.W, padx=5, pady=2)
        ttk.Radiobutton(metadata_frame, text="内部要求", variable=self.source_var,
                        value="内部要求").grid(row=2, column=1, sticky=tk.W, padx=5)
        ttk.Radiobutton(metadata_frame, text="客户要求", variable=self.source_var,
                        value="客户要求").grid(row=2, column=2, sticky=tk.W, padx=5)

        self.customer_label = ttk.Label(metadata_frame, text="客户:")
        self.customer_combo = ttk.Combobox(
            metadata_frame, textvariable=self.customer_var, state='readonly')

        metadata_frame.grid_columnconfigure(1, weight=1)

        # 3. 逻辑构造区
        logic_frame = ttk.LabelFrame(frame, text="逻辑构造", padding="10")
        logic_frame.pack(fill=tk.BOTH, expand=True)
        ttk.Button(logic_frame, text="添加条件块 (否则如果)",
                   command=self._add_condition_block).pack(pady=5)

        # 创建一个Canvas和Scrollbar来实现滚动
        canvas = tk.Canvas(logic_frame)
        scrollbar = ttk.Scrollbar(
            logic_frame, orient="vertical", command=canvas.yview)
        self.blocks_container = ttk.Frame(canvas)

        self.blocks_container.bind(
            "<Configure>", lambda e: canvas.configure(scrollregion=canvas.bbox("all")))
        canvas.create_window((0, 0), window=self.blocks_container, anchor="nw")
        canvas.configure(yscrollcommand=scrollbar.set)

        canvas.pack(side="left", fill="both", expand=True)
        scrollbar.pack(side="right", fill="y")

        # 绑定事件
        self.source_var.trace_add('write', self._handle_source_change)
        self.substance_var.trace_add('write', self._update_pseudocode_preview)
        self.customer_var.trace_add('write', self._update_pseudocode_preview)
        self.main_age_group_var.trace_add(
            'write', self._update_pseudocode_preview)

        return frame

    def _create_preview_panel(self, parent):
        frame = ttk.LabelFrame(parent, text="伪代码实时预览", padding="10")
        self.preview_text = tk.Text(
            frame, wrap=tk.WORD, state=tk.DISABLED, font=("Courier New", 10))

        v_scroll = ttk.Scrollbar(
            frame, orient=tk.VERTICAL, command=self.preview_text.yview)
        self.preview_text['yscrollcommand'] = v_scroll.set

        v_scroll.pack(side=tk.RIGHT, fill=tk.Y)
        self.preview_text.pack(fill=tk.BOTH, expand=True)
        return frame

    def _add_condition_block(self):
        block_frame = ttk.LabelFrame(
            self.blocks_container, text=f"条件块 #{len(self.condition_block_widgets) + 1}", padding="10")
        block_frame.pack(fill=tk.X, expand=True, padx=5, pady=5)

        # 条件行容器
        conditions_frame = ttk.Frame(block_frame)
        conditions_frame.pack(fill=tk.X, expand=True)

        # 行动逻辑 (THEN)
        then_frame = ttk.LabelFrame(block_frame, text="那么 (行动逻辑)", padding="5")
        then_frame.pack(fill=tk.X, expand=True, pady=(10, 0))

        ttk.Label(then_frame, text="限值要求:").grid(
            row=0, column=0, sticky=tk.W, padx=5)
        limit_entry = ttk.Entry(then_frame)
        limit_entry.grid(row=0, column=1, sticky='ew', padx=5)

        ttk.Label(then_frame, text="备注:").grid(
            row=1, column=0, sticky=tk.NW, padx=5)
        remarks_text = tk.Text(then_frame, height=3, width=30)
        remarks_text.grid(row=1, column=1, sticky='ew', padx=5, pady=2)
        then_frame.grid_columnconfigure(1, weight=1)

        # 为Limit Entry添加占位符行为
        limit_entry.insert(0, self.LIMIT_PLACEHOLDER)
        limit_entry.config(foreground="grey")
        limit_entry.bind(
            "<FocusIn>", lambda e: self._on_limit_focus_in(e.widget))
        limit_entry.bind(
            "<FocusOut>", lambda e: self._on_limit_focus_out(e.widget))
        limit_entry.bind("<KeyRelease>", self._update_pseudocode_preview)
        remarks_text.bind("<KeyRelease>", self._update_pseudocode_preview)

        # 存储控件引用
        block_data = {
            "frame": block_frame,
            "conditions_frame": conditions_frame,
            "condition_rows": [],
            "limit_entry": limit_entry,
            "remarks_text": remarks_text
        }
        self.condition_block_widgets.append(block_data)

        # 默认添加一个条件行
        self._add_condition_row(block_data)

        # 添加 "添加条件" 按钮
        add_cond_button = ttk.Button(
            conditions_frame, text="添加条件 (并且)", command=lambda b=block_data: self._add_condition_row(b))
        add_cond_button.pack(pady=5)

    def _add_condition_row(self, block_data):
        row_frame = ttk.Frame(block_data["conditions_frame"])
        row_frame.pack(fill=tk.X, expand=True, pady=2)

        # 判断字段
        field_var = tk.StringVar()
        field_combo = ttk.Combobox(row_frame, textvariable=field_var, values=[
                                   self.NO_SELECTION] + self.get_field_names(), state='readonly', width=15)
        field_combo.pack(side=tk.LEFT, padx=5, fill=tk.X, expand=True)
        field_var.set(self.NO_SELECTION)

        # 操作符
        op_var = tk.StringVar()
        op_combo = ttk.Combobox(row_frame, textvariable=op_var, values=[
                                '==', '包含于'], state='readonly', width=8)
        op_combo.pack(side=tk.LEFT, padx=5)
        op_var.set('==')

        # 值
        val_var = tk.StringVar()
        val_combo = ttk.Combobox(row_frame, textvariable=val_var, width=20)
        val_combo.pack(side=tk.LEFT, padx=5, fill=tk.X, expand=True)

        row_data = {"field": field_var, "operator": op_var,
                    "value": val_var, "value_combo": val_combo}
        block_data["condition_rows"].append(row_data)

        # 绑定事件，选择字段后动态更新值的下拉列表
        field_var.trace_add('write', lambda *args, fv=field_var,
                            vc=val_combo: self._update_value_options(fv, vc))

        # 绑定更新预览的事件
        for var in [field_var, op_var, val_var]:
            var.trace_add('write', self._update_pseudocode_preview)

    def _load_initial_data(self):
        """从数据库加载数据并填充所有下拉菜单。"""
        self.substance_list = self.db.get_all_substances()
        self.brand_list = self.db.get_all_brands()
        self.age_group_list = self.db.get_all_age_groups()
        self.location_list = self.db.get_all_component_locations()
        self.color_list = self.db.get_all_material_colors()
        self.tier_list = self.db.get_all_supplier_tiers()
        self.category_list = self.db.get_all_material_categories()

        self.substance_combo['values'] = self.substance_list
        self.customer_combo['values'] = self.brand_list

        # 如果有数据，设置默认值
        if self.substance_list:
            self.substance_var.set(self.substance_list[0])
        if self.brand_list:
            self.customer_var.set(self.brand_list[0])

        # 刷新所有现有的条件行
        for block in self.condition_block_widgets:
            for row in block['condition_rows']:
                self._update_value_options(row['field'], row['value_combo'])

        print("界面数据已刷新。")
        self._update_pseudocode_preview()

    def get_field_names(self):
        """返回条件字段的有序列表。"""
        return ["品牌/客户", "目标年龄段", "物料化学分类", "物料使用位置", "物料颜色", "供应商等级"]

    def _update_value_options(self, field_var, value_combo):
        field = field_var.get()
        options = []
        if field == "品牌/客户":
            options = self.brand_list
        elif field == "目标年龄段":
            options = self.age_group_list
        elif field == "物料化学分类":
            options = self.category_list
        elif field == "物料使用位置":
            options = self.location_list
        elif field == "物料颜色":
            options = self.color_list
        elif field == "供应商等级":
            options = self.tier_list

        value_combo['values'] = options
        if field == "目标年龄段":
            # 特殊处理：将第一个目标年龄段条件的值同步到主年龄段变量，用于规则名称生成
            value_combo.bind('<<ComboboxSelected>>', self._sync_main_age_group)
        else:
            # 解绑，避免其他字段影响规则名称
            try:
                value_combo.unbind('<<ComboboxSelected>>')
            except tk.TclError:
                pass  # 忽略解绑不存在的事件时可能出现的错误

    def _sync_main_age_group_internal(self):
        """内部方法：同步目标年龄段的值，不触发预览更新"""
        found_first = False
        for block in self.condition_block_widgets:
            for row in block['condition_rows']:
                if row['field'].get() == "目标年龄段":
                    self.main_age_group_var.set(row['value'].get())
                    found_first = True
                    break
            if found_first:
                break
        if not found_first:
            self.main_age_group_var.set("全年龄段")  # 如果没有设置，则使用默认值

    def _sync_main_age_group(self, event):
        """当目标年龄段的值改变时，更新用于规则名称的变量"""
        self._sync_main_age_group_internal()
        self._update_pseudocode_preview()

    def _handle_source_change(self, *args):
        source = self.source_var.get()
        if source == '客户要求':
            self.customer_label.grid(
                row=3, column=0, sticky=tk.W, padx=5, pady=2)
            self.customer_combo.grid(
                row=3, column=1, columnspan=3, sticky='ew', padx=5, pady=2)
        else:
            self.customer_label.grid_remove()
            self.customer_combo.grid_remove()
        self._update_pseudocode_preview()

    def _on_limit_focus_in(self, entry):
        if entry.get() == self.LIMIT_PLACEHOLDER:
            entry.delete(0, tk.END)
            entry.config(foreground="black")

    def _on_limit_focus_out(self, entry):
        if not entry.get():
            entry.insert(0, self.LIMIT_PLACEHOLDER)
            entry.config(foreground="grey")

    def _update_pseudocode_preview(self, *args):
        """核心方法：收集所有GUI数据，调用logic模块，并更新预览区。"""
        # 1. 收集数据
        rule_data = {}
        rule_data['substance'] = self.substance_var.get()
        rule_data['requirement_source'] = self.source_var.get()
        rule_data['limit_placeholder'] = self.LIMIT_PLACEHOLDER

        source_value = ""
        if rule_data['requirement_source'] == '客户要求':
            rule_data['customer'] = self.customer_var.get()
            source_value = rule_data['customer']
        else:
            rule_data['customer'] = None
            source_value = '内部要求'

        # 自动生成规则名称 - 直接同步age group而不调用方法避免循环
        self._sync_main_age_group_internal()  # 确保age group最新，但不触发预览更新
        age_group_for_name = self.main_age_group_var.get() or "全年龄段"
        rule_name = f"{source_value} - {age_group_for_name} - {rule_data['substance']} - 管控规则"
        self.rule_name_var.set(rule_name)
        rule_data['rule_name'] = rule_name
        rule_data['main_age_group'] = age_group_for_name

        rule_data['condition_blocks'] = []
        for block_widget in self.condition_block_widgets:
            block_info = {
                'limit': block_widget['limit_entry'].get(),
                'remarks': block_widget['remarks_text'].get("1.0", tk.END).strip(),
                'conditions': []
            }
            for row in block_widget['condition_rows']:
                condition_info = {
                    'field': row['field'].get(),
                    'operator': row['operator'].get(),
                    'value': row['value'].get()
                }
                block_info['conditions'].append(condition_info)
            rule_data['condition_blocks'].append(block_info)

        # 2. 调用逻辑模块
        pseudocode_text = generate_pseudocode(rule_data)

        # 3. 更新预览区
        self.preview_text.config(state=tk.NORMAL)
        self.preview_text.delete("1.0", tk.END)
        self.preview_text.insert("1.0", pseudocode_text)
        self.preview_text.config(state=tk.DISABLED)

    def _export_data(self):
        success, msg = self.db.export_to_excel()
        if success:
            messagebox.showinfo("成功", msg)
        else:
            messagebox.showerror("错误", msg)

    def _import_data(self):
        file_path = filedialog.askopenfilename(
            title="选择要导入的Excel文件",
            filetypes=[("Excel 文件", "*.xlsx *.xls")]
        )
        if not file_path:
            return

        if messagebox.askokcancel("确认导入", "这将清空并替换现有数据字典，确定要继续吗？"):
            success, msg = self.db.import_from_excel(file_path)
            if success:
                messagebox.showinfo("成功", msg)
                self._load_initial_data()  # 导入成功后刷新界面
            else:
                messagebox.showerror("错误", msg)

    def _populate_demo_data(self):
        if messagebox.askokcancel("确认操作", "这将清空所有数据并填充演示数据，确定要继续吗？"):
            self.db.clear_and_insert_demo_data()
            messagebox.showinfo("成功", "演示数据已成功填充！")
            self._load_initial_data()  # 填充后刷新界面
