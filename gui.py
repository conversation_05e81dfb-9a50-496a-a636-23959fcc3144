# gui.py

import tkinter as tk
from tkinter import ttk, messagebox, filedialog
from database import Database
from logic import generate_pseudocode


class Application(ttk.Frame):
    """
    主应用程序GUI类，负责构建和管理所有Tkinter界面元素和事件处理。
    """
    NO_SELECTION = "-- 不考虑此项 --"
    LIMIT_PLACEHOLDER = "引用相关表中的默认值"

    def __init__(self, master=None):
        super().__init__(master)
        self.master = master
        self.pack(fill="both", expand=True)

        self.db = Database()

        # 用于存储动态创建的控件引用
        self.condition_block_widgets = []

        # GUI状态变量
        self.source_var = tk.StringVar(value="内部要求")
        self.substance_var = tk.StringVar()
        self.customer_var = tk.StringVar()
        self.rule_name_var = tk.StringVar()
        self.main_age_group_var = tk.StringVar()  # 用于规则名称生成

        self._create_widgets()
        self._load_initial_data()
        self._load_custom_fields()  # 加载自定义字段配置
        self._add_condition_block()  # 默认创建一个条件块
        self._update_pseudocode_preview()

    def _create_widgets(self):
        # 使用PanedWindow创建可调整的双栏布局
        main_pane = ttk.PanedWindow(self, orient=tk.HORIZONTAL)
        main_pane.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)

        # 左侧：规则构造区
        constructor_frame = self._create_constructor_panel(main_pane)
        main_pane.add(constructor_frame, weight=1)

        # 右侧：伪代码预览区
        preview_frame = self._create_preview_panel(main_pane)
        main_pane.add(preview_frame, weight=1)

    def _create_constructor_panel(self, parent):
        frame = ttk.Frame(parent, padding="10")

        # 1. 数据管理区 - 固定在顶部，不扩展
        data_mgmt_frame = ttk.LabelFrame(frame, text="数据管理", padding="10")
        data_mgmt_frame.pack(fill=tk.X, expand=False, pady=(0, 10))

        # 第一行：导入导出按钮
        button_row1 = ttk.Frame(data_mgmt_frame)
        button_row1.pack(fill=tk.X, pady=(0, 5))
        ttk.Button(button_row1, text="导出数据字典 (Excel)",
                   command=self._export_data).pack(side=tk.LEFT, padx=5)
        ttk.Button(button_row1, text="导入数据字典 (Excel)",
                   command=self._import_data).pack(side=tk.LEFT, padx=5)
        ttk.Button(button_row1, text="填充演示数据",
                   command=self._populate_demo_data).pack(side=tk.LEFT, padx=5)

        # 第二行：自定义字段配置
        custom_field_row = ttk.Frame(data_mgmt_frame)
        custom_field_row.pack(fill=tk.X, pady=(5, 0))

        ttk.Label(custom_field_row, text="自定义字段:").pack(
            side=tk.LEFT, padx=(0, 5))

        self.custom_fields_var = tk.StringVar()
        self.custom_fields_entry = ttk.Entry(
            custom_field_row, textvariable=self.custom_fields_var, width=40)
        self.custom_fields_entry.pack(
            side=tk.LEFT, padx=5, fill=tk.X, expand=True)

        ttk.Button(custom_field_row, text="确认修改",
                   command=self._update_custom_fields).pack(side=tk.LEFT, padx=5)

        # 2. 规则元数据区 - 固定在顶部，不扩展
        metadata_frame = ttk.LabelFrame(frame, text="规则基础数据", padding="10")
        metadata_frame.pack(fill=tk.X, expand=False, pady=(0, 10))

        ttk.Label(metadata_frame, text="规则名称:").grid(
            row=0, column=0, sticky=tk.W, padx=5, pady=2)
        ttk.Entry(metadata_frame, textvariable=self.rule_name_var, state='readonly').grid(
            row=0, column=1, columnspan=3, sticky='ew', padx=5, pady=2)

        ttk.Label(metadata_frame, text="测试项目:").grid(
            row=1, column=0, sticky=tk.W, padx=5, pady=2)
        self.test_item_combo = ttk.Combobox(
            metadata_frame, textvariable=self.substance_var, state='readonly')
        self.test_item_combo.grid(
            row=1, column=1, columnspan=3, sticky='ew', padx=5, pady=2)

        ttk.Label(metadata_frame, text="要求来源:").grid(
            row=2, column=0, sticky=tk.W, padx=5, pady=2)
        ttk.Radiobutton(metadata_frame, text="内部要求", variable=self.source_var,
                        value="内部要求").grid(row=2, column=1, sticky=tk.W, padx=5)
        ttk.Radiobutton(metadata_frame, text="客户要求", variable=self.source_var,
                        value="客户要求").grid(row=2, column=2, sticky=tk.W, padx=5)

        self.customer_label = ttk.Label(metadata_frame, text="客户:")
        self.customer_combo = ttk.Combobox(
            metadata_frame, textvariable=self.customer_var, state='readonly')

        metadata_frame.grid_columnconfigure(1, weight=1)

        # 3. 逻辑构造区 - 这个区域可以扩展，占用剩余空间
        logic_frame = ttk.LabelFrame(frame, text="逻辑构造", padding="10")
        logic_frame.pack(fill=tk.BOTH, expand=True)
        ttk.Button(logic_frame, text="添加条件块",
                   command=self._add_condition_block).pack(pady=5)

        # 创建一个Canvas和Scrollbar来实现滚动
        canvas = tk.Canvas(logic_frame)
        scrollbar = ttk.Scrollbar(
            logic_frame, orient="vertical", command=canvas.yview)
        self.blocks_container = ttk.Frame(canvas)

        self.blocks_container.bind(
            "<Configure>", lambda e: canvas.configure(scrollregion=canvas.bbox("all")))
        canvas.create_window((0, 0), window=self.blocks_container, anchor="nw")
        canvas.configure(yscrollcommand=scrollbar.set)

        canvas.pack(side="left", fill="both", expand=True)
        scrollbar.pack(side="right", fill="y")

        # 绑定事件
        self.source_var.trace_add('write', self._handle_source_change)
        self.substance_var.trace_add('write', self._update_pseudocode_preview)
        self.customer_var.trace_add('write', self._update_pseudocode_preview)
        self.main_age_group_var.trace_add(
            'write', self._update_pseudocode_preview)

        return frame

    def _create_preview_panel(self, parent):
        # 创建主框架
        main_frame = ttk.Frame(parent, padding="10")

        # 创建标题栏框架，包含标题和复制按钮
        title_frame = ttk.Frame(main_frame)
        title_frame.pack(fill=tk.X, pady=(0, 10))

        # 标题标签
        title_label = ttk.Label(
            title_frame, text="规则预览", font=("微软雅黑", 10, "bold"))
        title_label.pack(side=tk.LEFT)

        # 按钮框架
        button_frame = ttk.Frame(title_frame)
        button_frame.pack(side=tk.RIGHT)

        # 复制按钮
        copy_button = ttk.Button(
            button_frame, text="复制", command=self._copy_preview_content)
        copy_button.pack(side=tk.LEFT, padx=(0, 5))

        # 复位按钮
        reset_button = ttk.Button(
            button_frame, text="复位", command=self._reset_interface)
        reset_button.pack(side=tk.LEFT)

        # 创建文本区域框架
        text_frame = ttk.Frame(main_frame)
        text_frame.pack(fill=tk.BOTH, expand=True)

        # 文本框和滚动条
        self.preview_text = tk.Text(
            text_frame, wrap=tk.WORD, state=tk.DISABLED, font=("微软雅黑", 12))

        v_scroll = ttk.Scrollbar(
            text_frame, orient=tk.VERTICAL, command=self.preview_text.yview)
        self.preview_text['yscrollcommand'] = v_scroll.set

        v_scroll.pack(side=tk.RIGHT, fill=tk.Y)
        self.preview_text.pack(fill=tk.BOTH, expand=True)
        return main_frame

    def _copy_preview_content(self):
        """复制预览区的内容到剪贴板"""
        try:
            # 获取预览区的所有文本内容
            content = self.preview_text.get("1.0", tk.END).strip()

            # 清空剪贴板并设置新内容
            self.master.clipboard_clear()
            self.master.clipboard_append(content)

            # 显示成功消息
            # messagebox.showinfo("复制成功", "伪代码内容已复制到剪贴板！")

        except Exception as e:
            messagebox.showerror("复制失败", f"复制到剪贴板时发生错误：{str(e)}")

    def _reset_interface(self):
        """复位界面到初始状态，不改变数据字典"""
        if messagebox.askokcancel("确认复位", "界面将恢复到初始状态"):
            try:
                # 1. 重置所有状态变量
                self.source_var.set("内部要求")
                self.main_age_group_var.set("")

                # 2. 重置测试项目和客户选择
                test_item_list = self.field_data.get('测试项目', [])
                if test_item_list:
                    self.substance_var.set(test_item_list[0])
                else:
                    self.substance_var.set("")

                brand_list = self.field_data.get('品牌客户', [])
                if brand_list:
                    self.customer_var.set(brand_list[0])
                else:
                    self.customer_var.set("")

                # 3. 清空所有条件块
                for block_data in self.condition_block_widgets:
                    block_data["frame"].destroy()
                self.condition_block_widgets.clear()

                # 4. 重新创建一个默认条件块
                self._add_condition_block()

                # 5. 更新预览
                self._update_pseudocode_preview()

            except Exception as e:
                messagebox.showerror("复位失败", f"复位界面时发生错误：{str(e)}")

    def _load_custom_fields(self):
        """加载自定义字段配置到输入框"""
        try:
            custom_fields = self.db.get_custom_fields()
            self.custom_fields_var.set(", ".join(custom_fields))
        except Exception as e:
            print(f"加载自定义字段配置失败: {e}")
            self.custom_fields_var.set("物料化学分类, 限用物质")

    def _update_custom_fields(self):
        """更新自定义字段配置"""
        try:
            # 获取用户输入的字段
            fields_text = self.custom_fields_var.get().strip()
            if not fields_text:
                messagebox.showerror("错误", "自定义字段不能为空")
                return

            # 解析字段列表
            custom_fields = [field.strip()
                             for field in fields_text.split(",") if field.strip()]

            # 验证字段数量
            if len(custom_fields) > 10:
                messagebox.showerror("错误", "最多只能设置10个自定义字段")
                return

            if len(custom_fields) == 0:
                messagebox.showerror("错误", "至少需要设置1个自定义字段")
                return

            # 更新数据库配置
            success, message = self.db.update_custom_fields(custom_fields)

            if success:
                messagebox.showinfo("成功", message)
                # 重新加载数据和界面
                self._load_initial_data()
                # 清空并重新创建条件块
                for block_data in self.condition_block_widgets:
                    block_data["frame"].destroy()
                self.condition_block_widgets.clear()
                self._add_condition_block()
                self._update_pseudocode_preview()
            else:
                messagebox.showerror("错误", message)

        except Exception as e:
            messagebox.showerror("错误", f"更新自定义字段时发生错误：{str(e)}")

    def _add_condition_block(self):
        block_frame = ttk.LabelFrame(
            self.blocks_container, text=f"条件块 #{len(self.condition_block_widgets) + 1}", padding="10")
        block_frame.pack(fill=tk.X, expand=True, padx=5, pady=5)

        # 条件行容器
        conditions_frame = ttk.Frame(block_frame)
        conditions_frame.pack(fill=tk.X, expand=True)

        # 行动逻辑 (THEN)
        then_frame = ttk.LabelFrame(block_frame, text="那么 (测试要求)", padding="5")
        then_frame.pack(fill=tk.X, expand=True, pady=(10, 0))

        ttk.Label(then_frame, text="标准要求:").grid(
            row=0, column=0, sticky=tk.W, padx=5)
        limit_entry = ttk.Entry(then_frame)
        limit_entry.grid(row=0, column=1, sticky='ew', padx=5)

        ttk.Label(then_frame, text="备注:").grid(
            row=1, column=0, sticky=tk.NW, padx=5)
        remarks_text = tk.Text(then_frame, height=3, width=30)
        remarks_text.grid(row=1, column=1, sticky='ew', padx=5, pady=2)
        then_frame.grid_columnconfigure(1, weight=1)

        # 为Limit Entry添加占位符行为
        limit_entry.insert(0, self.LIMIT_PLACEHOLDER)
        limit_entry.config(foreground="grey")
        limit_entry.bind(
            "<FocusIn>", lambda e: self._on_limit_focus_in(e.widget))
        limit_entry.bind(
            "<FocusOut>", lambda e: self._on_limit_focus_out(e.widget))
        limit_entry.bind("<KeyRelease>", self._update_pseudocode_preview)
        remarks_text.bind("<KeyRelease>", self._update_pseudocode_preview)

        # 存储控件引用
        block_data = {
            "frame": block_frame,
            "conditions_frame": conditions_frame,
            "condition_rows": [],
            "limit_entry": limit_entry,
            "remarks_text": remarks_text
        }
        self.condition_block_widgets.append(block_data)

        # 默认添加一个条件行
        self._add_condition_row(block_data)

        # 添加 "添加条件" 按钮
        add_cond_button = ttk.Button(
            conditions_frame, text="添加条件 (并且)", command=lambda b=block_data: self._add_condition_row(b))
        add_cond_button.pack(pady=5)

    def _add_condition_row(self, block_data):
        row_frame = ttk.Frame(block_data["conditions_frame"])
        row_frame.pack(fill=tk.X, expand=True, pady=2)

        # 判断字段
        field_var = tk.StringVar()
        field_combo = ttk.Combobox(row_frame, textvariable=field_var, values=[
                                   self.NO_SELECTION] + self.get_field_names(), state='readonly', width=15)
        field_combo.pack(side=tk.LEFT, padx=5, fill=tk.X, expand=True)
        field_var.set(self.NO_SELECTION)

        # 操作符
        op_var = tk.StringVar()
        op_combo = ttk.Combobox(row_frame, textvariable=op_var, values=[
                                '==', '包含于'], state='readonly', width=8)
        op_combo.pack(side=tk.LEFT, padx=5)
        op_var.set('==')

        # 值 - 创建一个框架来容纳不同的值选择控件
        value_frame = ttk.Frame(row_frame)
        value_frame.pack(side=tk.LEFT, padx=5, fill=tk.X, expand=True)

        # 单选下拉框（用于 == 操作符）
        val_var = tk.StringVar()
        val_combo = ttk.Combobox(value_frame, textvariable=val_var, width=20)
        val_combo.pack(fill=tk.X)

        # 多选列表框（用于 包含于 操作符）
        multi_select_frame = ttk.Frame(value_frame)
        multi_select_listbox = tk.Listbox(
            multi_select_frame, selectmode=tk.MULTIPLE, height=4)
        multi_select_scrollbar = ttk.Scrollbar(
            multi_select_frame, orient=tk.VERTICAL, command=multi_select_listbox.yview)
        multi_select_listbox.configure(
            yscrollcommand=multi_select_scrollbar.set)
        multi_select_listbox.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        multi_select_scrollbar.pack(side=tk.RIGHT, fill=tk.Y)

        # 默认显示单选下拉框
        multi_select_frame.pack_forget()

        row_data = {
            "field": field_var,
            "operator": op_var,
            "value": val_var,
            "value_combo": val_combo,
            "value_frame": value_frame,
            "multi_select_frame": multi_select_frame,
            "multi_select_listbox": multi_select_listbox,
            "selected_values": []  # 存储多选的值
        }
        block_data["condition_rows"].append(row_data)

        # 绑定事件，选择字段后动态更新值的下拉列表
        field_var.trace_add('write', lambda *args, fv=field_var,
                            vc=val_combo, rd=row_data: self._update_value_options(fv, vc, rd))

        # 绑定操作符变化事件，切换单选/多选界面
        op_var.trace_add('write', lambda *args,
                         rd=row_data: self._handle_operator_change(rd))

        # 绑定更新预览的事件
        for var in [field_var, op_var, val_var]:
            var.trace_add('write', self._update_pseudocode_preview)

        # 绑定多选列表框的选择事件
        multi_select_listbox.bind(
            '<<ListboxSelect>>', lambda e, rd=row_data: self._handle_multi_select_change(rd))

    def _load_initial_data(self):
        """从数据库加载数据并填充所有下拉菜单。"""
        # 动态加载所有字段的数据
        self.field_data = {}
        all_fields = self.db.get_all_field_names()

        for field_name in all_fields:
            self.field_data[field_name] = self.db.get_data_for_field(
                field_name)

        # 为了向后兼容，保留一些常用的属性
        self.brand_list = self.field_data.get('品牌客户', [])

        # 更新下拉菜单
        if hasattr(self, 'customer_combo'):
            self.customer_combo['values'] = self.brand_list
            if self.brand_list:
                self.customer_var.set(self.brand_list[0])

        # 加载测试项目数据
        test_item_list = self.field_data.get('测试项目', [])
        if hasattr(self, 'test_item_combo'):
            self.test_item_combo['values'] = test_item_list
            if test_item_list:
                self.substance_var.set(test_item_list[0])

        # 刷新所有现有的条件行
        for block in self.condition_block_widgets:
            for row in block['condition_rows']:
                self._update_value_options(
                    row['field'], row['value_combo'], row)

        print("界面数据已刷新。")
        self._update_pseudocode_preview()

    def get_field_names(self):
        """返回条件字段的有序列表。"""
        return self.db.get_all_field_names()

    def _update_value_options(self, field_var, value_combo, row_data=None):
        field = field_var.get()
        options = self.field_data.get(field, [])

        value_combo['values'] = options

        # 同时更新多选列表框的选项
        if row_data and 'multi_select_listbox' in row_data:
            listbox = row_data['multi_select_listbox']
            listbox.delete(0, tk.END)
            for option in options:
                listbox.insert(tk.END, option)

        if field == "目标年龄段":
            # 特殊处理：将第一个目标年龄段条件的值同步到主年龄段变量，用于规则名称生成
            value_combo.bind('<<ComboboxSelected>>', self._sync_main_age_group)
        else:
            # 解绑，避免其他字段影响规则名称
            try:
                value_combo.unbind('<<ComboboxSelected>>')
            except tk.TclError:
                pass  # 忽略解绑不存在的事件时可能出现的错误

    def _handle_operator_change(self, row_data):
        """处理操作符变化，切换单选/多选界面"""
        operator = row_data['operator'].get()

        if operator == '包含于':
            # 显示多选列表框，隐藏单选下拉框
            row_data['value_combo'].pack_forget()
            row_data['multi_select_frame'].pack(fill=tk.BOTH, expand=True)
        else:
            # 显示单选下拉框，隐藏多选列表框
            row_data['multi_select_frame'].pack_forget()
            row_data['value_combo'].pack(fill=tk.X)
            # 清空多选的值
            row_data['selected_values'] = []

        self._update_pseudocode_preview()

    def _handle_multi_select_change(self, row_data):
        """处理多选列表框的选择变化"""
        listbox = row_data['multi_select_listbox']
        selected_indices = listbox.curselection()
        selected_values = [listbox.get(i) for i in selected_indices]
        row_data['selected_values'] = selected_values

        # 更新单选框的值为多选值的逗号分隔字符串（用于伪代码生成）
        if selected_values:
            row_data['value'].set(', '.join(selected_values))
        else:
            row_data['value'].set('')

        self._update_pseudocode_preview()

    def _sync_main_age_group_internal(self):
        """内部方法：同步目标年龄段的值，不触发预览更新"""
        found_first = False
        for block in self.condition_block_widgets:
            for row in block['condition_rows']:
                if row['field'].get() == "目标年龄段":
                    self.main_age_group_var.set(row['value'].get())
                    found_first = True
                    break
            if found_first:
                break
        if not found_first:
            self.main_age_group_var.set("全年龄段")  # 如果没有设置，则使用默认值

    def _sync_main_age_group(self, event):
        """当目标年龄段的值改变时，更新用于规则名称的变量"""
        self._sync_main_age_group_internal()
        self._update_pseudocode_preview()

    def _handle_source_change(self, *args):
        source = self.source_var.get()
        if source == '客户要求':
            self.customer_label.grid(
                row=3, column=0, sticky=tk.W, padx=5, pady=2)
            self.customer_combo.grid(
                row=3, column=1, columnspan=3, sticky='ew', padx=5, pady=2)
        else:
            self.customer_label.grid_remove()
            self.customer_combo.grid_remove()
        self._update_pseudocode_preview()

    def _on_limit_focus_in(self, entry):
        if entry.get() == self.LIMIT_PLACEHOLDER:
            entry.delete(0, tk.END)
            entry.config(foreground="black")

    def _on_limit_focus_out(self, entry):
        if not entry.get():
            entry.insert(0, self.LIMIT_PLACEHOLDER)
            entry.config(foreground="grey")

    def _update_pseudocode_preview(self, *args):
        """核心方法：收集所有GUI数据，调用logic模块，并更新预览区。"""
        # 1. 收集数据
        rule_data = {}
        rule_data['substance'] = self.substance_var.get()
        rule_data['requirement_source'] = self.source_var.get()
        rule_data['limit_placeholder'] = self.LIMIT_PLACEHOLDER

        source_value = ""
        if rule_data['requirement_source'] == '客户要求':
            rule_data['customer'] = self.customer_var.get()
            source_value = rule_data['customer']
        else:
            rule_data['customer'] = None
            source_value = '内部要求'

        # 自动生成规则名称 - 直接同步age group而不调用方法避免循环
        self._sync_main_age_group_internal()  # 确保age group最新，但不触发预览更新
        age_group_for_name = self.main_age_group_var.get() or "全年龄段"
        rule_name = f"{source_value} - {age_group_for_name} - {rule_data['substance']}"
        self.rule_name_var.set(rule_name)
        rule_data['rule_name'] = rule_name
        rule_data['main_age_group'] = age_group_for_name

        rule_data['condition_blocks'] = []
        for block_widget in self.condition_block_widgets:
            block_info = {
                'limit': block_widget['limit_entry'].get(),
                'remarks': block_widget['remarks_text'].get("1.0", tk.END).strip(),
                'conditions': []
            }
            for row in block_widget['condition_rows']:
                condition_info = {
                    'field': row['field'].get(),
                    'operator': row['operator'].get(),
                    'value': row['value'].get()
                }
                block_info['conditions'].append(condition_info)
            rule_data['condition_blocks'].append(block_info)

        # 2. 调用逻辑模块
        pseudocode_text = generate_pseudocode(rule_data)

        # 3. 更新预览区
        self.preview_text.config(state=tk.NORMAL)
        self.preview_text.delete("1.0", tk.END)
        self.preview_text.insert("1.0", pseudocode_text)
        self.preview_text.config(state=tk.DISABLED)

    def _export_data(self):
        success, msg = self.db.export_to_excel()
        if success:
            messagebox.showinfo("成功", msg)
        else:
            messagebox.showerror("错误", msg)

    def _import_data(self):
        file_path = filedialog.askopenfilename(
            title="选择要导入的Excel文件",
            filetypes=[("Excel 文件", "*.xlsx *.xls")]
        )
        if not file_path:
            return

        if messagebox.askokcancel("确认导入", "这将清空并替换现有数据字典，确定要继续吗？"):
            success, msg = self.db.import_from_excel(file_path)
            if success:
                messagebox.showinfo("成功", msg)
                self._load_initial_data()  # 导入成功后刷新界面
            else:
                messagebox.showerror("错误", msg)

    def _populate_demo_data(self):
        if messagebox.askokcancel("确认操作", "这将清空所有数据并填充演示数据，确定要继续吗？"):
            self.db.clear_and_insert_demo_data()
            messagebox.showinfo("成功", "演示数据已成功填充！")
            self._load_initial_data()  # 填充后刷新界面
